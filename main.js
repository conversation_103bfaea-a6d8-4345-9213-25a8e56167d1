// Simple Quotes Generator - Clean and Fast
// Multiple API options in case one fails
const API_URLS = [

    'https://api.adviceslip.com/advice'
];

// Get DOM elements
const quoteElement = document.getElementById('quote');
const authorElement = document.getElementById('author');
const newQuoteBtn = document.getElementById('new-quote');
const copyBtn = document.getElementById('copy-quote');
const loadingElement = document.getElementById('loading');
const quoteContent = document.querySelector('.quote-content');

// Current quote data
let currentQuote = { content: '', author: '' };

// Fallback quotes if API fails
const fallbackQuotes = [
    { content: "The only way to do great work is to love what you do.", author: "Steve Jobs" },
    { content: "Innovation distinguishes between a leader and a follower.", author: "<PERSON> Jobs" },
    { content: "Life is what happens when you're busy making other plans.", author: "<PERSON>" },
    { content: "The future belongs to those who believe in the beauty of their dreams.", author: "<PERSON>" },
    { content: "It is during our darkest moments that we must focus to see the light.", author: "<PERSON>" }
];

// Get advice from AdviceSlip API
async function getNewQuote() {
    showLoading(true);

    try {
        const response = await fetch('https://api.adviceslip.com/advice');
        const data = await response.json();

        // AdviceSlip returns advice in different format
        if (data && data.slip && data.slip.advice) {
            currentQuote = {
                content: data.slip.advice,
                author: "Advice Slip"
            };
            displayQuote(data.slip.advice, "Advice Slip");
        } else {
            throw new Error('No advice received');
        }

    } catch (error) {
        // Fallback to built-in quotes
        const randomQuote = quotes[Math.floor(Math.random() * quotes.length)];
        currentQuote = randomQuote;
        displayQuote(randomQuote.content, randomQuote.author);
    }

    showLoading(false);
}

// Display quote with animation
function displayQuote(content, author) {
    quoteContent.style.opacity = '0';
    
    setTimeout(() => {
        quoteElement.textContent = `"${content}"`;
        authorElement.textContent = `— ${author}`;
        quoteContent.style.opacity = '1';
    }, 200);
}

// Show/hide loading
function showLoading(isLoading) {
    if (isLoading) {
        loadingElement.style.display = 'flex';
        quoteContent.style.display = 'none';
        newQuoteBtn.disabled = true;
        newQuoteBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
    } else {
        loadingElement.style.display = 'none';
        quoteContent.style.display = 'block';
        newQuoteBtn.disabled = false;
        newQuoteBtn.innerHTML = '<i class="fas fa-sync-alt"></i> New Quote';
    }
}

// Copy quote to clipboard
async function copyQuote() {
    const text = `"${currentQuote.content}" — ${currentQuote.author}`;
    
    try {
        await navigator.clipboard.writeText(text);
        showNotification('Quote copied!', 'success');
    } catch (error) {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showNotification('Quote copied!', 'success');
    }
}

// Show notification
function showNotification(message, type) {
    // Remove existing notifications
    const existing = document.querySelector('.notification');
    if (existing) existing.remove();
    
    // Create notification
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Show notification
    setTimeout(() => notification.classList.add('show'), 100);
    
    // Hide after 3 seconds
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

// Event listeners
newQuoteBtn.addEventListener('click', getNewQuote);
copyBtn.addEventListener('click', copyQuote);

// Keyboard shortcuts
document.addEventListener('keydown', (e) => {
    if (e.code === 'Space' && !newQuoteBtn.disabled) {
        e.preventDefault();
        getNewQuote();
    }
    if ((e.ctrlKey || e.metaKey) && e.code === 'KeyC') {
        e.preventDefault();
        copyQuote();
    }
});

// Load first quote when page loads
document.addEventListener('DOMContentLoaded', () => {
    getNewQuote();
});
